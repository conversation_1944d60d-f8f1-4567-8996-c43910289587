/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.query;

import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.druid.common.guava.GuavaUtils;
import org.apache.druid.java.util.common.ISE;
import org.apache.druid.java.util.common.StringUtils;
import org.apache.druid.java.util.common.guava.BaseSequence;
import org.apache.druid.java.util.common.guava.MergeIterable;
import org.apache.druid.java.util.common.guava.Sequence;
import org.apache.druid.java.util.common.logger.Logger;
import org.apache.druid.query.context.ResponseContext;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Simplified version of {@link ChainedExecutionQueryRunner} that delegates per-segment timeout handling
 * to {@link AsyncQueryRunner} implementations. This removes the complex timeout logic from the aggregating
 * runner and centralizes it in the segment runners.
 * 
 * This runner works with both regular {@link QueryRunner} and {@link AsyncQueryRunner} implementations.
 * For AsyncQueryRunner instances, it uses their async interface. For regular QueryRunner instances,
 * it wraps them in immediate futures.
 */
public class SimplifiedChainedExecutionQueryRunner<T> implements QueryRunner<T>
{
  private static final Logger log = new Logger(SimplifiedChainedExecutionQueryRunner.class);

  private final Iterable<QueryRunner<T>> queryables;
  private final QueryWatcher queryWatcher;

  public SimplifiedChainedExecutionQueryRunner(
      QueryWatcher queryWatcher,
      Iterable<QueryRunner<T>> queryables
  )
  {
    this.queryables = Iterables.unmodifiableIterable(queryables);
    this.queryWatcher = queryWatcher;
  }

  @Override
  public Sequence<T> run(final QueryPlus<T> queryPlus, final ResponseContext responseContext)
  {
    Query<T> query = queryPlus.getQuery();
    final Ordering ordering = query.getResultOrdering();
    final QueryPlus<T> threadSafeQueryPlus = queryPlus.withoutThreadUnsafeState();
    final QueryContext context = query.context();

    return new BaseSequence<>(
        new BaseSequence.IteratorMaker<>()
        {
          @Override
          public Iterator<T> make()
          {
            // Convert all runners to async futures
            List<ListenableFuture<Sequence<T>>> futures = StreamSupport.stream(queryables.spliterator(), false)
                .map(runner -> {
                  if (runner == null) {
                    throw new ISE("Null queryRunner! Looks to be some segment unmapping action happening");
                  }

                  // Use async interface if available, otherwise wrap in immediate future
                  if (runner instanceof AsyncQueryRunner) {
                    return ((AsyncQueryRunner<T>) runner).runAsync(threadSafeQueryPlus, responseContext);
                  } else {
                    // Fallback for non-async runners - execute synchronously and wrap in future
                    try {
                      Sequence<T> result = runner.run(threadSafeQueryPlus, responseContext);
                      return Futures.immediateFuture(result);
                    } catch (Exception e) {
                      return Futures.immediateFailedFuture(e);
                    }
                  }
                })
                .collect(Collectors.toList());

            ListenableFuture<List<Sequence<T>>> allFutures = Futures.allAsList(futures);
            queryWatcher.registerQueryFuture(query, allFutures);

            try {
              List<Sequence<T>> sequences;
              if (context.hasTimeout()) {
                sequences = allFutures.get(context.getTimeout(), TimeUnit.MILLISECONDS);
              } else {
                sequences = allFutures.get();
              }

              // Convert sequences to lists and merge
              List<Iterable<T>> iterables = sequences.stream()
                  .map(Sequence::toList)
                  .collect(Collectors.toList());

              return new MergeIterable<>(iterables, ordering.nullsFirst()).iterator();
            }
            catch (InterruptedException e) {
              log.noStackTrace().warn(e, "Query interrupted, cancelling pending results, query id [%s]", query.getId());
              GuavaUtils.cancelAll(true, allFutures, futures);
              throw new QueryInterruptedException(e);
            }
            catch (CancellationException e) {
              throw new QueryInterruptedException(e);
            }
            catch (TimeoutException e) {
              log.warn("Query timeout, cancelling pending results for query id [%s]", query.getId());
              GuavaUtils.cancelAll(true, allFutures, futures);
              throw new QueryTimeoutException(StringUtils.nonStrictFormat("Query [%s] timed out", query.getId()));
            }
            catch (ExecutionException e) {
              GuavaUtils.cancelAll(true, allFutures, futures);
              Throwable cause = e.getCause();
              Throwables.propagateIfPossible(cause);
              if (cause instanceof TimeoutException || cause instanceof QueryTimeoutException) {
                log.info("Query timeout, cancelling pending results for query id [%s]", query.getId());
                throw new QueryTimeoutException(StringUtils.nonStrictFormat("Query [%s] timed out", query.getId()));
              }
              throw new RuntimeException(e);
            }
          }

          @Override
          public void cleanup(Iterator<T> tIterator)
          {
            // No cleanup needed
          }
        }
    );
  }
}
