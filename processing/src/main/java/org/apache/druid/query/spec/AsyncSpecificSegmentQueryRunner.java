/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.query.spec;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.druid.java.util.common.guava.Sequence;
import org.apache.druid.query.AbstractPrioritizedQueryRunnerCallable;
import org.apache.druid.query.AsyncQueryRunner;
import org.apache.druid.query.QueryContext;
import org.apache.druid.query.QueryPlus;
import org.apache.druid.query.QueryProcessingPool;
import org.apache.druid.query.QueryRunner;
import org.apache.druid.query.context.ResponseContext;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

/**
 * Async version of {@link SpecificSegmentQueryRunner} that handles per-segment timeouts internally.
 * This simplifies the logic in aggregating runners like {@link org.apache.druid.query.ChainedExecutionQueryRunner}
 * by moving timeout handling to the segment level.
 */
public class AsyncSpecificSegmentQueryRunner<T> implements AsyncQueryRunner<T>
{
  private final SpecificSegmentQueryRunner<T> delegate;
  private final QueryProcessingPool queryProcessingPool;

  public AsyncSpecificSegmentQueryRunner(
      SpecificSegmentQueryRunner<T> delegate,
      @Nullable QueryProcessingPool queryProcessingPool
  )
  {
    this.delegate = delegate;
    this.queryProcessingPool = queryProcessingPool;
  }

  @Override
  public Sequence<T> run(QueryPlus<T> queryPlus, ResponseContext responseContext)
  {
    // Delegate to the original SpecificSegmentQueryRunner for synchronous execution
    return delegate.run(queryPlus, responseContext);
  }

  @Override
  public ListenableFuture<Sequence<T>> runAsync(QueryPlus<T> queryPlus, ResponseContext responseContext)
  {
    final QueryContext context = queryPlus.getQuery().context();

    // If per-segment timeout is enabled and we have a processing pool, use async execution with timeout
    if (context.usePerSegmentTimeout() && queryProcessingPool != null) {
      final int priority = context.getPriority();
      final long perSegmentTimeout = context.getPerSegmentTimeout();

      final AbstractPrioritizedQueryRunnerCallable<Sequence<T>, T> callable =
          new AbstractPrioritizedQueryRunnerCallable<Sequence<T>, T>(priority, delegate)
          {
            @Override
            public Sequence<T> call()
            {
              return delegate.run(queryPlus, responseContext);
            }
          };

      return queryProcessingPool.submitRunnerTask(callable, perSegmentTimeout, TimeUnit.MILLISECONDS);
    } else {
      // No timeout or no processing pool, execute synchronously and return immediate future
      try {
        Sequence<T> result = delegate.run(queryPlus, responseContext);
        return Futures.immediateFuture(result);
      } catch (Exception e) {
        return Futures.immediateFailedFuture(e);
      }
    }
  }

  /**
   * Factory method to create an AsyncSpecificSegmentQueryRunner from a base QueryRunner and SpecificSegmentSpec.
   * This is a convenience method that wraps the base runner in a SpecificSegmentQueryRunner first.
   */
  public static <T> AsyncSpecificSegmentQueryRunner<T> create(
      QueryRunner<T> base,
      SpecificSegmentSpec specificSpec,
      @Nullable QueryProcessingPool queryProcessingPool
  )
  {
    SpecificSegmentQueryRunner<T> specificRunner = new SpecificSegmentQueryRunner<>(base, specificSpec);
    return new AsyncSpecificSegmentQueryRunner<>(specificRunner, queryProcessingPool);
  }
}
