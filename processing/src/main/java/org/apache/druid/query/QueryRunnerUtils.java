/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.query;

import org.apache.druid.query.spec.AsyncSpecificSegmentQueryRunner;
import org.apache.druid.query.spec.SpecificSegmentQueryRunner;

import javax.annotation.Nullable;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Utility methods for working with QueryRunners, particularly for migrating to the simplified
 * per-segment timeout approach using AsyncQueryRunner.
 */
public class QueryRunnerUtils
{
  /**
   * Creates a simplified chained execution query runner that delegates per-segment timeout handling
   * to AsyncQueryRunner implementations. This is a drop-in replacement for ChainedExecutionQueryRunner
   * that removes the complex timeout logic from the aggregating runner.
   * 
   * @param queryProcessingPool the query processing pool (used for creating async segment runners)
   * @param queryWatcher the query watcher
   * @param queryRunners the query runners to chain
   * @param <T> the result type
   * @return a simplified chained execution query runner
   */
  public static <T> QueryRunner<T> createSimplifiedChainedRunner(
      @Nullable QueryProcessingPool queryProcessingPool,
      QueryWatcher queryWatcher,
      Iterable<QueryRunner<T>> queryRunners
  )
  {
    // Convert SpecificSegmentQueryRunner instances to AsyncSpecificSegmentQueryRunner
    Iterable<QueryRunner<T>> asyncRunners = StreamSupport.stream(queryRunners.spliterator(), false)
        .map(runner -> {
          if (runner instanceof SpecificSegmentQueryRunner && queryProcessingPool != null) {
            // Wrap SpecificSegmentQueryRunner in AsyncSpecificSegmentQueryRunner for timeout handling
            return new AsyncSpecificSegmentQueryRunner<>((SpecificSegmentQueryRunner<T>) runner, queryProcessingPool);
          } else {
            // Keep other runners as-is
            return runner;
          }
        })
        .collect(Collectors.toList());

    return new SimplifiedChainedExecutionQueryRunner<>(queryWatcher, asyncRunners);
  }

  /**
   * Backward compatibility method that creates a ChainedExecutionQueryRunner or SimplifiedChainedExecutionQueryRunner
   * based on a system property. This allows for gradual migration.
   * 
   * @param queryProcessingPool the query processing pool
   * @param queryWatcher the query watcher
   * @param queryRunners the query runners to chain
   * @param <T> the result type
   * @return a chained execution query runner
   */
  public static <T> QueryRunner<T> createChainedRunner(
      QueryProcessingPool queryProcessingPool,
      QueryWatcher queryWatcher,
      Iterable<QueryRunner<T>> queryRunners
  )
  {
    // For now, use the system property to control which implementation to use
    // In the future, this can default to the simplified version
    boolean useSimplified = Boolean.parseBoolean(
        System.getProperty("druid.query.useSimplifiedChainedRunner", "false")
    );

    if (useSimplified) {
      return createSimplifiedChainedRunner(queryProcessingPool, queryWatcher, queryRunners);
    } else {
      return new ChainedExecutionQueryRunner<>(queryProcessingPool, queryWatcher, queryRunners);
    }
  }
}
