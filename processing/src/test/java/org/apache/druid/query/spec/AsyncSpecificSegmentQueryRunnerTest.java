/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.query.spec;

import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.druid.java.util.common.Lifecycle;
import org.apache.druid.java.util.common.guava.Sequence;
import org.apache.druid.java.util.common.guava.Sequences;
import org.apache.druid.query.DruidProcessingConfig;
import org.apache.druid.query.ForwardingQueryProcessingPool;
import org.apache.druid.query.PrioritizedExecutorService;
import org.apache.druid.query.Query;
import org.apache.druid.query.QueryContexts;
import org.apache.druid.query.QueryInterruptedException;
import org.apache.druid.query.QueryPlus;
import org.apache.druid.query.QueryRunner;
import org.apache.druid.query.QueryTimeoutException;
import org.apache.druid.query.SegmentDescriptor;
import org.apache.druid.query.aggregation.CountAggregatorFactory;
import org.apache.druid.query.context.ResponseContext;
import org.apache.druid.query.timeseries.TimeseriesQuery;
import org.apache.druid.query.timeseries.TimeseriesResultValue;
import org.apache.druid.timeline.SegmentId;
import org.joda.time.Interval;
import org.apache.druid.java.util.common.Intervals;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

public class AsyncSpecificSegmentQueryRunnerTest
{
  private ExecutorService executorService;
  private ForwardingQueryProcessingPool queryProcessingPool;

  @Before
  public void setUp()
  {
    executorService = PrioritizedExecutorService.create(
        new Lifecycle(),
        new DruidProcessingConfig()
        {
          @Override
          public String getFormatString()
          {
            return "test";
          }

          @Override
          public int getNumThreads()
          {
            return 2;
          }
        }
    );
    queryProcessingPool = new ForwardingQueryProcessingPool(executorService);
  }

  @After
  public void tearDown()
  {
    executorService.shutdown();
  }

  @Test
  public void testSyncExecution() throws Exception
  {
    // Create a simple query runner that returns a fixed result
    QueryRunner<TimeseriesResultValue> baseRunner = (queryPlus, responseContext) ->
        Sequences.simple(Collections.singletonList(new TimeseriesResultValue(ImmutableMap.of("count", 42))));

    SpecificSegmentSpec segmentSpec = new SpecificSegmentSpec(
        new SegmentDescriptor(Intervals.of("2014/2015"), "version", 0)
    );

    AsyncSpecificSegmentQueryRunner<TimeseriesResultValue> asyncRunner =
        new AsyncSpecificSegmentQueryRunner<>(
            new SpecificSegmentQueryRunner<>(baseRunner, segmentSpec),
            queryProcessingPool
        );

    TimeseriesQuery query = new TimeseriesQuery(
        null,
        null,
        false,
        null,
        null,
        Collections.singletonList(Intervals.of("2014/2015")),
        null,
        Collections.singletonList(new CountAggregatorFactory("count")),
        null,
        null,
        ImmutableMap.of() // No timeout
    );

    // Test sync execution
    Sequence<TimeseriesResultValue> syncResult = asyncRunner.run(QueryPlus.wrap(query), ResponseContext.createEmpty());
    Assert.assertEquals(1, syncResult.toList().size());
    Assert.assertEquals(42, syncResult.toList().get(0).getValue().get("count"));

    // Test async execution
    ListenableFuture<Sequence<TimeseriesResultValue>> asyncResult = asyncRunner.runAsync(
        QueryPlus.wrap(query),
        ResponseContext.createEmpty()
    );
    Sequence<TimeseriesResultValue> result = asyncResult.get(1, TimeUnit.SECONDS);
    Assert.assertEquals(1, result.toList().size());
    Assert.assertEquals(42, result.toList().get(0).getValue().get("count"));
  }

  @Test
  public void testPerSegmentTimeout() throws Exception
  {
    // Create a slow query runner that takes longer than the timeout
    QueryRunner<TimeseriesResultValue> slowRunner = (queryPlus, responseContext) -> {
      try {
        Thread.sleep(500); // Sleep for 500ms
      } catch (InterruptedException e) {
        throw new QueryInterruptedException(e);
      }
      return Sequences.simple(Collections.singletonList(new TimeseriesResultValue(ImmutableMap.of("count", 42))));
    };

    SpecificSegmentSpec segmentSpec = new SpecificSegmentSpec(
        new SegmentDescriptor(Intervals.of("2014/2015"), "version", 0)
    );

    AsyncSpecificSegmentQueryRunner<TimeseriesResultValue> asyncRunner =
        new AsyncSpecificSegmentQueryRunner<>(
            new SpecificSegmentQueryRunner<>(slowRunner, segmentSpec),
            queryProcessingPool
        );

    TimeseriesQuery query = new TimeseriesQuery(
        null,
        null,
        false,
        null,
        null,
        Collections.singletonList(Intervals.of("2014/2015")),
        null,
        Collections.singletonList(new CountAggregatorFactory("count")),
        null,
        null,
        ImmutableMap.of(QueryContexts.PER_SEGMENT_TIMEOUT_KEY, 100L) // 100ms timeout
    );

    // Test that async execution times out
    ListenableFuture<Sequence<TimeseriesResultValue>> asyncResult = asyncRunner.runAsync(
        QueryPlus.wrap(query),
        ResponseContext.createEmpty()
    );

    Exception thrown = null;
    try {
      asyncResult.get(1, TimeUnit.SECONDS);
    } catch (Exception e) {
      thrown = e;
    }

    Assert.assertNotNull("Expected timeout exception", thrown);
    Assert.assertTrue(
        "Should be timeout-related exception",
        thrown.getCause() instanceof java.util.concurrent.TimeoutException ||
        thrown.getCause() instanceof QueryTimeoutException
    );
  }

  @Test
  public void testNoTimeoutWithoutProcessingPool() throws Exception
  {
    // Create a slow query runner
    QueryRunner<TimeseriesResultValue> slowRunner = (queryPlus, responseContext) -> {
      try {
        Thread.sleep(200); // Sleep for 200ms
      } catch (InterruptedException e) {
        throw new QueryInterruptedException(e);
      }
      return Sequences.simple(Collections.singletonList(new TimeseriesResultValue(ImmutableMap.of("count", 42))));
    };

    SpecificSegmentSpec segmentSpec = new SpecificSegmentSpec(
        new SegmentDescriptor(Intervals.of("2014/2015"), "version", 0)
    );

    // Create async runner without processing pool
    AsyncSpecificSegmentQueryRunner<TimeseriesResultValue> asyncRunner =
        new AsyncSpecificSegmentQueryRunner<>(
            new SpecificSegmentQueryRunner<>(slowRunner, segmentSpec),
            null // No processing pool
        );

    TimeseriesQuery query = new TimeseriesQuery(
        null,
        null,
        false,
        null,
        null,
        Collections.singletonList(Intervals.of("2014/2015")),
        null,
        Collections.singletonList(new CountAggregatorFactory("count")),
        null,
        null,
        ImmutableMap.of(QueryContexts.PER_SEGMENT_TIMEOUT_KEY, 100L) // 100ms timeout
    );

    // Without processing pool, timeout should be ignored and query should succeed
    ListenableFuture<Sequence<TimeseriesResultValue>> asyncResult = asyncRunner.runAsync(
        QueryPlus.wrap(query),
        ResponseContext.createEmpty()
    );

    Sequence<TimeseriesResultValue> result = asyncResult.get(1, TimeUnit.SECONDS);
    Assert.assertEquals(1, result.toList().size());
    Assert.assertEquals(42, result.toList().get(0).getValue().get("count"));
  }
}
